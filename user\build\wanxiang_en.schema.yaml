__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012324
    default.custom: 1749539007
    key_bindings: 1749012324
    key_bindings.custom: 0
    punctuation: 1749012323
    punctuation.custom: 0
    wanxiang_en.custom: 1749482762
    wanxiang_en.schema: 1749482762
algebra_common:
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
engine:
  filters:
    - uniquifier
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
key_binder:
  bindings:
    - {accept: "Control+p", send: Up, when: composing}
    - {accept: "Control+n", send: Down, when: composing}
    - {accept: "Control+b", send: Left, when: composing}
    - {accept: "Control+f", send: Right, when: composing}
    - {accept: "Control+a", send: Home, when: composing}
    - {accept: "Control+e", send: End, when: composing}
    - {accept: "Control+d", send: Delete, when: composing}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: "Control+h", send: BackSpace, when: composing}
    - {accept: "Control+g", send: Escape, when: composing}
    - {accept: "Control+bracketleft", send: Escape, when: composing}
    - {accept: "Control+y", send: Page_Up, when: composing}
    - {accept: "Alt+v", send: Page_Up, when: composing}
    - {accept: "Control+v", send: Page_Down, when: composing}
    - {accept: ISO_Left_Tab, send: "Shift+Left", when: composing}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: comma, send: Page_Up, when: paging}
    - {accept: period, send: Page_Down, when: has_menu}
    - {accept: "Control+Shift+1", select: .next, when: always}
    - {accept: "Control+Shift+2", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+3", toggle: full_shape, when: always}
    - {accept: "Control+Shift+4", toggle: simplification, when: always}
    - {accept: "Control+Shift+5", toggle: extended_charset, when: always}
    - {accept: "Control+Shift+exclam", select: .next, when: always}
    - {accept: "Control+Shift+at", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+numbersign", toggle: full_shape, when: always}
    - {accept: "Control+Shift+dollar", toggle: simplification, when: always}
    - {accept: "Control+Shift+percent", toggle: extended_charset, when: always}
  import_preset: default
menu:
  page_size: 5
recognizer:
  import_preset: default
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema:
  description: "Easy English Nano，只包含少量常用词汇，方便中英文混合输入度方案调用。"
  name: "英文"
  schema_id: wanxiang_en
  version: "2023-10-17"
set_shuru_schema:
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/ly/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/jy/"
speller:
  algebra:
    - "derive/1([4-7|9])/$1teen/"
    - "derive/11/eleven/"
    - "derive/12/twelve/"
    - "derive/13/thirteen/"
    - "derive/15/fifteen/"
    - "derive/18/eighteen/"
    - "derive/0/o/"
    - "derive/0/O/"
    - "derive/0/zero/"
    - "derive/1/one/"
    - "derive/10/ten/"
    - "derive/2/to/"
    - "derive/2/two/"
    - "derive/3/three/"
    - "derive/4/for/"
    - "derive/4/four/"
    - "derive/5/five/"
    - "derive/6/six/"
    - "derive/7/seven/"
    - "derive/8/eight/"
    - "derive/9/nine/"
    - "derive/\\+/plus/"
    - "derive/\\./dot/"
    - "derive/@/at/"
    - "derive/-/hyphen/"
    - "derive/#/hash/"
    - "derive/#/number/"
    - "derive/#/sharp/"
    - "derive/♯/sharp/"
    - "derive / slash"
    - "derive/&/and/"
    - "derive/%/percent/"
    - "derive/[.]//"
    - "derive/[+]//"
    - "derive/[@]//"
    - "derive/[-]//"
    - "derive/[_]//"
    - "derive/[^a-zA-Z0-9]//"
    - "erase/^[^a-zA-Z0-9].+$/"
    - "derive/^.+$/\\L$0/"
    - "derive/^.+$/\\U$0/"
    - "derive/^./\\U$0/"
    - "derive/^([a-z]{2})/\\U$1/"
    - "derive/^([a-z]{3})/\\U$1/"
    - "derive/^([a-z]{4})/\\U$1/"
    - "derive/^([a-z]{5})/\\U$1/"
    - "derive/^([a-z]{6})/\\U$1/"
    - "derive/^([a-z]{7})/\\U$1/"
    - "derive/^([a-z]{8})/\\U$1/"
    - "derive/^([a-z]{9})/\\U$1/"
    - "derive/^([a-z]{10})/\\U$1/"
    - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
    - "derive/([1-9])0000(?!0)/$1wj/"
    - "derive/([1-9])000(?!0)/$1qm/"
    - "derive/([1-9])00(?!0)/$1bd/"
    - "derive/([2-9])0(?!0)/$1ui/"
    - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
    - "derive/\\./dm/"
    - "derive/10/ui/"
    - "derive/0/lk/"
    - "derive/1/yi/"
    - "derive/2/er/"
    - "derive/2/ll/"
    - "derive/3/sj/"
    - "derive/4/si/"
    - "derive/5/wu/"
    - "derive/6/lq/"
    - "derive/7/qi/"
    - "derive/8/ba/"
    - "derive/9/jq/"
    - "derive/\\+/jx/"
    - "derive/#/jk/"
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA
  delimiter: " '"
switches:
  - name: ascii_mode
    reset: 0
    states: ["ASCII-OFF", "ASCII-ON"]
translator:
  dictionary: wanxiang_en
  spelling_hints: 9
"全拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/shi$1/"
  - "derive/([1-9])0000(?!0)/$1wan/"
  - "derive/([1-9])000(?!0)/$1qian/"
  - "derive/([1-9])00(?!0)/$1bai/"
  - "derive/([2-9])0(?!0)/$1shi/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1shi$2/"
  - "derive/\\./dian/"
  - "derive/10/shi/"
  - "derive/0/ling/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/liang/"
  - "derive/3/san/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/liu/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jiu/"
  - "derive/\\+/jia/"
  - "derive/#/jing/"
"小鹤双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bd/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/lk/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ll/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jx/"
  - "derive/#/jk/"
"微软双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/j;/"
"搜狗双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/jy/"
"智能ABC":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/vi$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qw/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1vi/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1vi$2/"
  - "derive/\\./dw/"
  - "derive/10/vi/"
  - "derive/0/ly/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/or/"
  - "derive/2/lt/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lr/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jr/"
  - "derive/\\+/jd/"
  - "derive/#/jy/"
"紫光双拼":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ii$1/"
  - "derive/([1-9])0000(?!0)/$1wr/"
  - "derive/([1-9])000(?!0)/$1qf/"
  - "derive/([1-9])00(?!0)/$1bp/"
  - "derive/([2-9])0(?!0)/$1ii/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ii$2/"
  - "derive/\\./df/"
  - "derive/10/ii/"
  - "derive/0/l;/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/oj/"
  - "derive/2/lg/"
  - "derive/3/sr/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lj/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jj/"
  - "derive/\\+/jx/"
  - "derive/#/j;/"
"自然码":
  - "derive/1([4-7|9])/$1teen/"
  - "derive/11/eleven/"
  - "derive/12/twelve/"
  - "derive/13/thirteen/"
  - "derive/15/fifteen/"
  - "derive/18/eighteen/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/zero/"
  - "derive/1/one/"
  - "derive/10/ten/"
  - "derive/2/to/"
  - "derive/2/two/"
  - "derive/3/three/"
  - "derive/4/for/"
  - "derive/4/four/"
  - "derive/5/five/"
  - "derive/6/six/"
  - "derive/7/seven/"
  - "derive/8/eight/"
  - "derive/9/nine/"
  - "derive/\\+/plus/"
  - "derive/\\./dot/"
  - "derive/@/at/"
  - "derive/-/hyphen/"
  - "derive/#/hash/"
  - "derive/#/number/"
  - "derive/#/sharp/"
  - "derive/♯/sharp/"
  - "derive / slash"
  - "derive/&/and/"
  - "derive/%/percent/"
  - "derive/[.]//"
  - "derive/[+]//"
  - "derive/[@]//"
  - "derive/[-]//"
  - "derive/[_]//"
  - "derive/[^a-zA-Z0-9]//"
  - "erase/^[^a-zA-Z0-9].+$/"
  - "derive/^.+$/\\L$0/"
  - "derive/^.+$/\\U$0/"
  - "derive/^./\\U$0/"
  - "derive/^([a-z]{2})/\\U$1/"
  - "derive/^([a-z]{3})/\\U$1/"
  - "derive/^([a-z]{4})/\\U$1/"
  - "derive/^([a-z]{5})/\\U$1/"
  - "derive/^([a-z]{6})/\\U$1/"
  - "derive/^([a-z]{7})/\\U$1/"
  - "derive/^([a-z]{8})/\\U$1/"
  - "derive/^([a-z]{9})/\\U$1/"
  - "derive/^([a-z]{10})/\\U$1/"
  - "derive/(?<!\\d)1([1-9])(?!\\d)/ui$1/"
  - "derive/([1-9])0000(?!0)/$1wj/"
  - "derive/([1-9])000(?!0)/$1qm/"
  - "derive/([1-9])00(?!0)/$1bl/"
  - "derive/([2-9])0(?!0)/$1ui/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1ui$2/"
  - "derive/\\./dm/"
  - "derive/10/ui/"
  - "derive/0/ly/"
  - "derive/1/yi/"
  - "derive/2/er/"
  - "derive/2/ld/"
  - "derive/3/sj/"
  - "derive/4/si/"
  - "derive/5/wu/"
  - "derive/6/lq/"
  - "derive/7/qi/"
  - "derive/8/ba/"
  - "derive/9/jq/"
  - "derive/\\+/jw/"
  - "derive/#/jy/"