__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012324
    default.custom: 1749539007
    key_bindings: 1749012324
    key_bindings.custom: 0
    punctuation: 1749012324
    punctuation.custom: 0
config_version: 0.40
good_old_caps_lock: true
key_binder:
  bindings:
    - {accept: "Control+p", send: Up, when: composing}
    - {accept: "Control+n", send: Down, when: composing}
    - {accept: "Control+b", send: Left, when: composing}
    - {accept: "Control+f", send: Right, when: composing}
    - {accept: "Control+a", send: Home, when: composing}
    - {accept: "Control+e", send: End, when: composing}
    - {accept: "Control+d", send: Delete, when: composing}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: "Control+h", send: BackSpace, when: composing}
    - {accept: "Control+g", send: Escape, when: composing}
    - {accept: "Control+bracketleft", send: Escape, when: composing}
    - {accept: "Control+y", send: Page_Up, when: composing}
    - {accept: "Alt+v", send: Page_Up, when: composing}
    - {accept: "Control+v", send: Page_Down, when: composing}
    - {accept: ISO_Left_Tab, send: "Shift+Left", when: composing}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: comma, send: Page_Up, when: paging}
    - {accept: period, send: Page_Down, when: has_menu}
    - {accept: "Control+Shift+1", select: .next, when: always}
    - {accept: "Control+Shift+2", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+3", toggle: full_shape, when: always}
    - {accept: "Control+Shift+4", toggle: simplification, when: always}
    - {accept: "Control+Shift+5", toggle: extended_charset, when: always}
    - {accept: "Control+Shift+exclam", select: .next, when: always}
    - {accept: "Control+Shift+at", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+numbersign", toggle: full_shape, when: always}
    - {accept: "Control+Shift+dollar", toggle: simplification, when: always}
    - {accept: "Control+Shift+percent", toggle: extended_charset, when: always}
menu:
  page_size: 5
punctuator:
  full_shape:
    " ": {commit: "　"}
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": ["＃", "⌘"]
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["％", "°", "℃"]
    "&": "＆"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["＊", "·", "・", "×", "※", "❂"]
    "+": "＋"
    ",": {commit: "，"}
    "-": "－"
    .: {commit: "。"}
    "/": ["／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "＝"
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": ["＠", "☯"]
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "｀"
    "{": ["『", "〖", "｛"]
    "|": ["·", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": "～"
  half_shape:
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": "#"
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["%", "％", "°", "℃"]
    "&": "&"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["*", "＊", "·", "・", "×", "※", "❂"]
    "+": "+"
    ",": {commit: "，"}
    "-": "-"
    .: {commit: "。"}
    "/": ["、", "/", "／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "="
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": "@"
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "\\", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "`"
    "{": ["『", "〖", "｛"]
    "|": ["·", "|", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": ["~", "～"]
recognizer:
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema_list:
  - schema: wanxiang
switch_key:
  Caps_Lock: clear
  Control_L: noop
  Control_R: noop
  Shift_L: noop
  Shift_R: inline_ascii
switcher:
  abbreviate_options: true
  caption: "〔方案選單〕"
  fold_options: true
  hotkeys:
    - "Control+grave"
    - "Control+Shift+grave"
    - F4
  option_list_separator: "／"
  save_options:
    - full_shape
    - ascii_punct
    - simplification
    - extended_charset
    - zh_hant
    - zh_hans
    - zh_hant_tw