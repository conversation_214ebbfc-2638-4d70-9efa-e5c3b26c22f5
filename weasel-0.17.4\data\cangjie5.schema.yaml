# Rime schema settings
# encoding: utf-8

schema:
  schema_id: cangjie5
  name: 倉頡五代
  version: "0.30"
  author:
    - 發明人 朱邦復先生
  description: |
    第五代倉頡輸入法
    碼表源自倉頡之友發佈的《五倉世紀版》
    www.chinesecj.com
  dependencies:
    - luna_quanpin

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]
  - name: simplification
    states: [ 漢字, 汉字 ]
  - name: extended_charset
    states: [ 常用, 增廣 ]
  - name: ascii_punct
    states: [ 。，, ．， ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - reverse_lookup_translator
    - table_translator
  filters:
    - simplifier
    - uniquifier
    - single_char_filter

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcba
  delimiter: " ;"
  #max_code_length: 5  # 五碼頂字上屏

translator:
  dictionary: cangjie5
  enable_charset_filter: true
  enable_sentence: true
  enable_encoder: true
  encode_commit_history: true
  max_phrase_length: 5
  preedit_format:
    - 'xform/^([a-z]*)$/$1\t（\U$1\E）/'
    - "xlit|ABCDEFGHIJKLMNOPQRSTUVWXYZ|日月金木水火土竹戈十大中一弓人心手口尸廿山女田難卜符|"
  comment_format:
    - "xlit|abcdefghijklmnopqrstuvwxyz~|日月金木水火土竹戈十大中一弓人心手口尸廿山女田難卜符～|"
  disable_user_dict_for_patterns:
    - "^z.*$"
    - "^yyy.*$"

abc_segmentor:
  extra_tags:
    - reverse_lookup  # 與拼音（反查碼）混打

reverse_lookup:
  dictionary: luna_pinyin
  prism: luna_quanpin
  prefix: "`"
  suffix: "'"
  tips: 〔拼音〕
  preedit_format:
    - xform/([nl])v/$1ü/
    - xform/([nl])ue/$1üe/
    - xform/([jqxy])v/$1u/
  comment_format:
    - "xlit|abcdefghijklmnopqrstuvwxyz|日月金木水火土竹戈十大中一弓人心手口尸廿山女田難卜符|"

simplifier:
  tips: all  # 簡化字模式下提示對應的傳統漢字

punctuator:
  import_preset: symbols

key_binder:
  import_preset: default

recognizer:
  import_preset: default
  patterns:
    punct: '^/([0-9]0?|[A-Za-z]+)$'
    reverse_lookup: "`[a-z]*'?$|[a-z]+'$"
