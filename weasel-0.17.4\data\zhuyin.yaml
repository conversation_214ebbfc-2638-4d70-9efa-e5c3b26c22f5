# Rime spelling algebra rules for Zhuyin

pinyin_to_zhuyin:
  __append:
    - xform/^m(\d)$/mu$1/  # 呣
    - xform/^r5$/er5/  # 〜兒
    - xform/iu/iou/
    - xform/ui/uei/
    - xform/ong/ung/
    - xform/^yi?/i/
    - xform/^wu?/u/
    - xform/iu/v/
    - xform/^([jqx])u/$1v/
    - xform/([iuv])n/$1en/
    - xform/^zhi?/Z/
    - xform/^chi?/C/
    - xform/^shi?/S/
    - xform/^([zcsr])i/$1/
    - xform/ai/A/
    - xform/ei/I/
    - xform/ao/O/
    - xform/ou/U/
    - xform/ang/K/
    - xform/eng/G/
    - xform/an/M/
    - xform/en/N/
    - xform/er/R/
    - xform/eh/E/
    - xform/([iv])e/$1E/

free_order:
  __append:
    - derive/([bpmfdtnlgkhjqxZCSrzcs])([iuv])/$2$1/
    - derive/([iuv])([aoeEAIOUMNKGR])/$2$1/
    - derive/([bpmfdtnlgkhjqxZCSrzcs])([aoeEAIOUMNKGR])/$2$1/
    - derive/([bpmfdtnlgkhjqxZCSrzcs])([iuv])/$2$1/

abbreviation:
  __append:
    - abbrev/^([bpmfdtnlgkhjqxZCSrzcs]).+$/$1/
    - abbrev/^([A-Za-z]+)\d$/$1/
    - abbrev/^([bpmfdtnlgkhjqxZCSrzcs]).+(\d)$/$1$2/

keymap_bopomofo:
  __append:
    - 'xlit|bpmfdtnlgkhjqxZCSrzcsiuvaoeEAIOUMNKGR12345|1qaz2wsxedcrfv5tgbyhnujm8ik,9ol.0p;/- 6347|'
