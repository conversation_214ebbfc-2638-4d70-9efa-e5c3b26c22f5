# Rime default settings
# encoding: utf-8

config_version: '0.40'

schema_list:
  - schema: luna_pinyin
  - schema: luna_pinyin_simp
  - schema: luna_pinyin_fluency
  - schema: bopomofo
  - schema: bopomofo_tw
  - schema: cangjie5
  - schema: stroke
  - schema: terra_pinyin

switcher:
  caption: 〔方案選單〕
  hotkeys:
    - Control+grave
    - Control+Shift+grave
    - F4
  save_options:
    - full_shape
    - ascii_punct
    - simplification
    - extended_charset
    - zh_hant
    - zh_hans
    - zh_hant_tw
  fold_options: true
  abbreviate_options: true
  option_list_separator: '／'

menu:
  page_size: 5

punctuator:
  full_shape:
    __include: punctuation:/full_shape
  half_shape:
    __include: punctuation:/half_shape

key_binder:
  bindings:
    __patch:
      - key_bindings:/emacs_editing
      - key_bindings:/move_by_word_with_tab
      - key_bindings:/paging_with_minus_equal
      - key_bindings:/paging_with_comma_period
      - key_bindings:/numbered_mode_switch

recognizer:
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"

ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Shift_L: inline_ascii
    Shift_R: commit_text
    Control_L: noop
    Control_R: noop
    Caps_Lock: clear
    Eisu_toggle: clear
