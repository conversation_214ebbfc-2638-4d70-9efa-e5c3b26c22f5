customization:
  distribution_code_name: Weasel
  distribution_version: 0.17.4
  generator: "Rime::SwitcherSettings"
  modified_time: "Tue Jun 10 10:30:48 2025"
  rime_version: 1.13.1
patch:
  schema_list:
    - {schema: wanxiang}
# 中西文切换
#
# good_old_caps_lock:
# true   切换大写
# false  切换中英
# macOS 偏好设置的优先级更高，如果勾选【使用大写锁定键切换“ABC”输入法】则始终会切换输入法。
#
# 切换中英：
# 不同的选项表示：打字打到一半时按下了 CapsLock、Shift、Control 后：
# commit_code  上屏原始的编码，然后切换到英文
# commit_text  上屏拼出的词句，然后切换到英文
# clear        清除未上屏内容，然后切换到英文
# inline_ascii 切换到临时英文模式，按回车上屏后回到中文状态
# noop         屏蔽快捷键，不切换中英，但不要屏蔽 CapsLock
  ascii_composer:
  good_old_caps_lock: true  # true | false
  switch_key:
    Caps_Lock: clear      # commit_code | commit_text | clear
    Shift_L: noop  # commit_code | commit_text | inline_ascii | clear | noop
    Shift_R: inline_ascii         # commit_code | commit_text | inline_ascii | clear | noop
    Control_L: noop       # commit_code | commit_text | inline_ascii | clear | noop
    Control_R: noop       # commit_code | commit_text | inline_ascii | clear | noop