# Rime key bindings
# encoding: utf-8

emacs_editing:
  __append:
    - { when: composing, accept: Control+p, send: Up }
    - { when: composing, accept: Control+n, send: Down }
    - { when: composing, accept: Control+b, send: Left }
    - { when: composing, accept: Control+f, send: Right }
    - { when: composing, accept: Control+a, send: Home }
    - { when: composing, accept: Control+e, send: End }
    - { when: composing, accept: Control+d, send: Delete }
    - { when: composing, accept: Control+k, send: Shift+Delete }
    - { when: composing, accept: Control+h, send: BackSpace }
    - { when: composing, accept: Control+g, send: Escape }
    - { when: composing, accept: Control+bracketleft, send: Escape }
    - { when: composing, accept: Control+y, send: Page_Up }
    - { when: composing, accept: Alt+v, send: Page_Up }
    - { when: composing, accept: Control+v, send: Page_Down }

move_by_word_with_tab:
  __append:
    - { when: composing, accept: ISO_Left_Tab, send: Shift+Left }
    - { when: composing, accept: Shift+Tab, send: Shift+Left }
    - { when: composing, accept: Tab, send: Shift+Right }

paging_with_minus_equal:
  __append:
    - { when: has_menu, accept: minus, send: Page_Up }
    - { when: has_menu, accept: equal, send: Page_Down }

paging_with_comma_period:
  __append:
    - { when: paging, accept: comma, send: Page_Up }
    - { when: has_menu, accept: period, send: Page_Down }

paging_with_brackets:
  __append:
    - { when: paging, accept: bracketleft, send: Page_Up }
    - { when: has_menu, accept: bracketright, send: Page_Down }

numbered_mode_switch:
  __append:
    - { when: always, accept: Control+Shift+1, select: .next }
    - { when: always, accept: Control+Shift+2, toggle: ascii_mode }
    - { when: always, accept: Control+Shift+3, toggle: full_shape }
    - { when: always, accept: Control+Shift+4, toggle: simplification }
    - { when: always, accept: Control+Shift+5, toggle: extended_charset }
    - { when: always, accept: Control+Shift+exclam, select: .next }
    - { when: always, accept: Control+Shift+at, toggle: ascii_mode }
    - { when: always, accept: Control+Shift+numbersign, toggle: full_shape }
    - { when: always, accept: Control+Shift+dollar, toggle: simplification }
    - { when: always, accept: Control+Shift+percent, toggle: extended_charset }

windows_compatible_mode_switch:
  __append:
    - { when: always, accept: Shift+space, toggle: full_shape }
    - { when: always, accept: Control+period, toggle: ascii_punct }

optimized_mode_switch:
  __append:
    - { when: always, accept: Control+Shift+space, select: .next }
    - { when: always, accept: Shift+space, toggle: ascii_mode }
    - { when: always, accept: Control+comma, toggle: full_shape }
    - { when: always, accept: Control+period, toggle: ascii_punct }
    - { when: always, accept: Control+slash, toggle: simplification }
    - { when: always, accept: Control+backslash, toggle: extended_charset }
