# Rime basic symbols
# encoding: utf-8

full_shape:
  ' ' : { commit: '　' }
  ',' : { commit: ， }
  '.' : { commit: 。 }
  '<' : [ 《, 〈, «, ‹ ]
  '>' : [ 》, 〉, », › ]
  '/' : [ ／, ÷ ]
  '?' : { commit: ？ }
  ';' : { commit: ； }
  ':' : { commit: ： }
  '''' : { pair: [ '‘', '’' ] }
  '"' : { pair: [ '“', '”' ] }
  '\' : [ 、, ＼ ]
  '|' : [ ·, ｜, '§', '¦' ]
  '`' : ｀
  '~' : ～
  '!' : { commit: ！ }
  '@' : [ ＠, ☯ ]
  '#' : [ ＃, ⌘ ]
  '%' : [ ％, '°', '℃' ]
  '$' : [ ￥, '$', '€', '£', '¥', '¢', '¤' ]
  '^' : { commit: …… }
  '&' : ＆
  '*' : [ ＊, ·, ・, ×, ※, ❂ ]
  '(' : （
  ')' : ）
  '-' : －
  '_' : ——
  '+' : ＋
  '=' : ＝
  '[' : [ 「, 【, 〔, ［ ]
  ']' : [ 」, 】, 〕, ］ ]
  '{' : [ 『, 〖, ｛ ]
  '}' : [ 』, 〗, ｝ ]

half_shape:
  ',' : { commit: ， }
  '.' : { commit: 。 }
  '<' : [ 《, 〈, «, ‹ ]
  '>' : [ 》, 〉, », › ]
  '/' : [ 、, '/', ／, ÷ ]
  '?' : { commit: ？ }
  ';' : { commit: ； }
  ':' : { commit: ： }
  '''' : { pair: [ '‘', '’' ] }
  '"' : { pair: [ '“', '”' ] }
  '\' : [ 、, '\', ＼ ]
  '|' : [ ·, '|', ｜, '§', '¦' ]
  '`' : '`'
  '~' : [ '~', ～ ]
  '!' : { commit: ！ }
  '@' : '@'
  '#' : '#'
  '%' : [ '%', ％, '°', '℃' ]
  '$' : [ ￥, '$', '€', '£', '¥', '¢', '¤' ]
  '^' : { commit: …… }
  '&' : '&'
  '*' : [ '*', ＊, ·, ・, ×, ※, ❂ ]
  '(' : （
  ')' : ）
  '-' : '-'
  '_' : ——
  '+' : '+'
  '=' : '='
  '[' : [ 「, 【, 〔, ［ ]
  ']' : [ 」, 】, 〕,  ］ ]
  '{' : [ 『, 〖, ｛ ]
  '}' : [ 』, 〗, ｝ ]

ascii_style:
  ',' : { commit: ',' }
  '.' : { commit: '.' }
  '<' : '<'
  '>' : '>'
  '/' : '/'
  '?' : { commit: '?' }
  ';' : { commit: ';' }
  ':' : { commit: ':' }
  "'" : "'"
  '"' : '"'
  '\' : '\'
  '|' : '|'
  '`' : '`'
  '~' : '~'
  '!' : { commit: '!' }
  '@' : '@'
  '#' : '#'
  '%' : '%'
  '$' : '$'
  '^' : '^'
  '&' : '&'
  '*' : '*'
  '(' : '('
  ')' : ')'
  '-' : '-'
  '_' : '_'
  '+' : '+'
  '=' : '='
  '[' : '['
  ']' : ']'
  '{' : '{'
  '}' : '}'
