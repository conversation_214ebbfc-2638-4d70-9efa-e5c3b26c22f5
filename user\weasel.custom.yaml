customization:
  distribution_code_name: Weasel
  distribution_version: 0.17.4
  generator: "Weasel::UIStyleSettings"
  modified_time: "Tue Jun 10 10:30:52 2025"
  rime_version: 1.13.1
patch:
  # 启用状态切换通知
  show_notifications: true
  show_notifications_time: 1500
  # 全局ASCII模式设置
  global_ascii: false

  # 应用特定设置
  app_options:
    cmd.exe:
      ascii_mode: true
    Code.exe:
      ascii_mode: false

  # 样式设置
  style:
    # 启用托盘图标
    display_tray_icon: true
    # ASCII模式提示跟随光标
    ascii_tip_follow_cursor: true
    # 增强定位
    enhanced_position: true
    # 布局设置
    horizontal: true
    inline_preedit: true
    color_scheme: lost_temple
    # 布局参数
    layout:
      baseline: 0                            # 字号百分比，与 linespacing 一同设置可解决字体跳动问题，设置为 0 为禁用
      linespacing: 0                         # 字号百分比，参考 <https://github.com/rime/weasel/pull/1177>
      align_type: center                     # 标签、候选文字、注解文字之间的相对对齐方式：top ; center ; bottom
      max_height: 600                        # 候选框最大高度，文本竖排模式下如高度超此尺寸则换列显示候选，设置为 0 不启用此功能
      max_width: 0                           # 候选框最大宽度，horizontal 布局如宽超此尺寸则换行显示候选，设置为 0 不启用此功能
      min_height: 0                          # 候选框最小高度
      min_width: 10                          # 候选框最小宽度
      border_width: 3                        # 边框宽度；又名 border
      margin_x: 12                           # 主体元素和候选框的左右边距；为负值时，不显示候选框
      margin_y: 12                           # 主体元素的上下边距；为负值时，不显示候选框
      spacing: 13                            # inline_preedit 为否时，编码区域和候选区域的间距
      candidate_spacing: 22                  # 候选项之间的间距
      hilite_spacing: 6                      # 候选项和相应标签的间距
      hilite_padding: 4                      # 高亮区域和内部文字的间距，影响高亮区域大小
      shadow_radius: 0                       # 阴影区域半径，为 0 不显示阴影；需要同时在配色方案中指定非透明的阴影颜色
      shadow_offset_x: 6                     # 阴影左右偏移距离
      shadow_offset_y: 4                     # 阴影上下偏移距离
      corner_radius: 8                       # 候选窗口圆角半径
      round_corner: 8                        # 候选背景色块圆角半径，又名 hilited_corner_radius

  # 配色方案
  preset_color_schemes:
    lost_temple:
      name: 孤寺／Lost Temple
      author: 佛振 <<EMAIL>>, based on ir_black
      text_color: 15266806
      back_color: 4473924
      border_color: 4473924
      label_color: 11647162
      hilited_text_color: 8578762
      hilited_back_color: 2236962
      candidate_text_color: 15266806
      comment_text_color: 11647162
      hilited_candidate_text_color: 0
      hilited_comment_text_color: 11647162
      hilited_candidate_back_color: 8578762
      hilited_label_color: 2837571