# Rime schema
# encoding: utf-8

schema:
  schema_id: bopomofo
  name: 注音
  version: '2.4'
  author:
    - 佛振 <<EMAIL>>
  description: |
    注音符號輸入，採用「大千式」鍵盤排列。

    本方案採用「無模式」設計，以大寫字母鍵或上下方向鍵、回車鍵選詞；
    空格鍵輸入第一聲，標記爲「ˉ」。

    請配合 librime>=1.3 使用。

  dependencies:
    - stroke

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]
  - options:
      - zh_hant
      - zh_hans
      - zh_hant_tw
    states:
      - 傳統漢字
      - 简化字
      - 臺灣字形

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - fluency_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - table_translator@custom_phrase
    - reverse_lookup_translator
    - script_translator
  filters:
    - simplifier@zh_hans
    - simplifier@zh_hant_tw
    - uniquifier

menu:
  alternative_select_keys: "ABCDEFGHIJ"

speller:
  alphabet: '1qaz2wsxedcrfv5tgbyhnujm8ik,9ol.0p;/- 6347'
  initials: '1qaz2wsxedcrfv5tgbyhnujm8ik,9ol.0p;/-'
  finals: " 6347"
  delimiter: "'"
  use_space: true
  algebra:
    __patch:
      - zhuyin:/pinyin_to_zhuyin
      - zhuyin:/free_order
      - zhuyin:/abbreviation
      - zhuyin:/keymap_bopomofo

translator:
  dictionary: terra_pinyin
  prism: bopomofo
  preedit_format:
    - "xlit|1qaz2wsxedcrfv5tgbyhnujm8ik,9ol.0p;/- 6347'|ㄅㄆㄇㄈㄉㄊㄋㄌㄍㄎㄏㄐㄑㄒㄓㄔㄕㄖㄗㄘㄙㄧㄨㄩㄚㄛㄜㄝㄞㄟㄠㄡㄢㄣㄤㄥㄦˉˊˇˋ˙ |"

custom_phrase:
  dictionary: ""
  user_dict: custom_phrase
  db_class: stabledb
  enable_completion: false
  enable_sentence: false
  initial_quality: 1

reverse_lookup:
  dictionary: stroke
  enable_completion: true
  prefix: "`"
  suffix: "'"
  tips: 〔筆畫〕
  preedit_format:
    - xlit/hspnz/一丨丿丶乙/
  comment_format:
    - xform/e?r5$/er5/
    - xform/iu/iou/
    - xform/ui/uei/
    - xform/ong/ung/
    - xform/^yi?/i/
    - xform/^wu?/u/
    - xform/iu/v/
    - xform/^([jqx])u/$1v/
    - xform/([iuv])n/$1en/
    - xform/zh/Z/
    - xform/ch/C/
    - xform/sh/S/
    - xform/ai/A/
    - xform/ei/I/
    - xform/ao/O/
    - xform/ou/U/
    - xform/ang/K/
    - xform/eng/G/
    - xform/an/M/
    - xform/en/N/
    - xform/er/R/
    - xform/eh/E/
    - xform/([iv])e/$1E/
    - xform/1//
    - 'xlit|bpmfdtnlgkhjqxZCSrzcsiuvaoeEAIOUMNKGR2345|ㄅㄆㄇㄈㄉㄊㄋㄌㄍㄎㄏㄐㄑㄒㄓㄔㄕㄖㄗㄘㄙㄧㄨㄩㄚㄛㄜㄝㄞㄟㄠㄡㄢㄣㄤㄥㄦˊˇˋ˙|'

punctuator:
  full_shape:
    " " : { commit: "　" }
    "<" : { commit: "，" }
    ">" : { commit: "。" }
    "?" : [ ？, ／, ÷ ]
    ":" : [ ：, ； ]
    "'" : { pair: [ "‘", "’" ] }
    "\"" : { pair: [ "“", "”" ] }
    "\\" : [ 、, ＼ ]
    "|" : [ ·, ｜, "§", "¦" ]
    "`" : ｀
    "~" : ～
    "!" : { commit: ！ }
    "@" : [ ＠, ☯ ]
    "#" : [ ＃, ⌘ ]
    "%" : [ ％, "°", "℃" ]
    "$" : [ ￥, "$", "€", "£", "¥", "¢", "¤" ]
    "^" : { commit: …… }
    "&" : ＆
    "*" : [ ＊, ·, ・, ×, ※, ❂ ]
    "(" : （
    ")" : ）
    "_" : [ ——, － ]
    "+" : ＋
    "=" : ＝
    "[" : [ 「, 【, 《, 〔, ［ ]
    "]" : [ 」, 】, 》, 〕, ］ ]
    "{" : [ 『, 〖, 〈, ｛ ]
    "}" : [ 』, 〗, 〉, ｝ ]
  half_shape:
    "<" : { commit: "，" }
    ">" : { commit: "。" }
    "?" : [ ？, ／, "/", ÷ ]
    ":" : [ ：, ； ]
    "'" : { pair: [ "‘", "’" ] }
    "\"" : { pair: [ "“", "”" ] }
    "\\" : [ 、, "\\", ＼ ]
    "|" : [ ·, "|", ｜, "§", "¦" ]
    "`" : "`"
    "~" : "~"
    "!" : { commit: ！ }
    "@" : "@"
    "#" : "#"
    "%" : [ "%", ％, "°", "℃" ]
    "$" : [ ￥, "$", "€", "£", "¥", "¢", "¤" ]
    "^" : { commit: …… }
    "&" : "&"
    "*" : [ "*", ＊, ·, ・, ×, ※, ❂ ]
    "(" : （
    ")" : ）
    "_" : [ ——, －, "-" ]
    "+" : "+"
    "=" : "="
    "[" : [ 「, 【, 《, 〔, ［ ]
    "]" : [ 」, 】, 》, 〕, ］ ]
    "{" : [ 『, 〖, 〈, ｛ ]
    "}" : [ 』, 〗, 〉, ｝ ]

editor:
  bindings:
    space: toggle_selection

key_binder:
  import_preset: default
  bindings:
    - { when: has_menu, accept: comma, send: comma }      # ㄝ
    - { when: has_menu, accept: period, send: period }    # ㄡ
    - { when: has_menu, accept: minus, send: minus }      # ㄦ
    - { when: paging, accept: minus, send: Page_Up }
    - { when: has_menu, accept: equal, send: Page_Down }

recognizer:
  patterns:
    email: "^[a-z][-_.0-9a-z]*@.*$"
    uppercase: "^[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp:|mailto:).*$"
    reverse_lookup: "`[a-z]*'?$"

zh_hans:
  option_name: zh_hans
  opencc_config: t2s.json
  tips: all
  excluded_types: [ reverse_lookup ]

zh_hant_tw:
  option_name: zh_hant_tw
  opencc_config: t2tw.json
  tips: none
  excluded_types: [ reverse_lookup ]

__patch:
  # 使用八股文語言模型
  - grammar:/hant?
  # 用家自選配置
  - bopomofo.custom:/patch?
