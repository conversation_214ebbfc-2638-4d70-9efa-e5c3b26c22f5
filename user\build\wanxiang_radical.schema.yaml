__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012323
    default.custom: 1749539007
    key_bindings: 1749012324
    key_bindings.custom: 0
    punctuation: 1749012323
    punctuation.custom: 0
    wanxiang_radical.custom: 1749482762
    wanxiang_radical.schema: 1749482762
engine:
  filters:
    - uniquifier
  processors:
    - key_binder
    - speller
    - selector
    - navigator
    - express_editor
  segmentors:
    - abc_segmentor
  translators:
    - echo_translator
    - table_translator
key_binder:
  bindings:
    - {accept: "Control+p", send: Up, when: composing}
    - {accept: "Control+n", send: Down, when: composing}
    - {accept: "Control+b", send: Left, when: composing}
    - {accept: "Control+f", send: Right, when: composing}
    - {accept: "Control+a", send: Home, when: composing}
    - {accept: "Control+e", send: End, when: composing}
    - {accept: "Control+d", send: Delete, when: composing}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: "Control+h", send: BackSpace, when: composing}
    - {accept: "Control+g", send: Escape, when: composing}
    - {accept: "Control+bracketleft", send: Escape, when: composing}
    - {accept: "Control+y", send: Page_Up, when: composing}
    - {accept: "Alt+v", send: Page_Up, when: composing}
    - {accept: "Control+v", send: Page_Down, when: composing}
    - {accept: ISO_Left_Tab, send: "Shift+Left", when: composing}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: comma, send: Page_Up, when: paging}
    - {accept: period, send: Page_Down, when: has_menu}
    - {accept: "Control+Shift+1", select: .next, when: always}
    - {accept: "Control+Shift+2", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+3", toggle: full_shape, when: always}
    - {accept: "Control+Shift+4", toggle: simplification, when: always}
    - {accept: "Control+Shift+5", toggle: extended_charset, when: always}
    - {accept: "Control+Shift+exclam", select: .next, when: always}
    - {accept: "Control+Shift+at", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+numbersign", toggle: full_shape, when: always}
    - {accept: "Control+Shift+dollar", toggle: simplification, when: always}
    - {accept: "Control+Shift+percent", toggle: extended_charset, when: always}
menu:
  page_size: 5
schema:
  author: Mirtle
  name: "反查：部件组字"
  schema_id: wanxiang_radical
  version: 1.1.0
set_shuru_schema:
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/'|<|>//"
speller:
  algebra:
    - "derive/^([jqxy])u(?=^|$|')/$1v/"
    - "derive/'([jqxy])u(?=^|$|')/'$1v/"
    - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
    - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
    - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
    - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
    - "xform/iu(?=^|$|')/<q>/"
    - "xform/(.)ei(?=^|$|')/$1<w>/"
    - "xform/uan(?=^|$|')/<r>/"
    - "xform/[uv]e(?=^|$|')/<t>/"
    - "xform/un(?=^|$|')/<y>/"
    - "xform/^sh/<u>/"
    - "xform/^ch/<i>/"
    - "xform/^zh/<v>/"
    - "xform/'sh/'<u>/"
    - "xform/'ch/'<i>/"
    - "xform/'zh/'<v>/"
    - "xform/uo(?=^|$|')/<o>/"
    - "xform/ie(?=^|$|')/<p>/"
    - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
    - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
    - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
    - "xform/([a-z>])en(?=^|$|')/$1<f>/"
    - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
    - "xform/[iu]ang(?=^|$|')/<l>/"
    - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
    - "xform/ian(?=^|$|')/<m>/"
    - "xform/([a-z>])an(?=^|$|')/$1<j>/"
    - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
    - "xform/[iu]a(?=^|$|')/<x>/"
    - "xform/iao(?=^|$|')/<n>/"
    - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
    - "xform/ui(?=^|$|')/<v>/"
    - "xform/in(?=^|$|')/<b>/"
    - "xform/'|<|>//"
  alphabet: "abcdefghijklmnopqrstuvwxyz;"
  delimiter: " '"
translator:
  dictionary: wanxiang_radical
  enable_user_dict: false
"全拼":
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
"小鹤双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/(.)ei(?=^|$|')/$1<w>/"
  - "xform/uan(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/un(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ie(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/[iu]ang(?=^|$|')/<l>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<n>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/in(?=^|$|')/<b>/"
  - "xform/'|<|>//"
"微软双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "derive/<t>(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"搜狗双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"智能ABC":
  - "xform/^zh/<a>/"
  - "xform/^ch/<e>/"
  - "xform/^sh/<v>/"
  - "xform/'zh/'<a>/"
  - "xform/'ch/'<e>/"
  - "xform/'sh/'<v>/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/ei(?=^|$|')/<q>/"
  - "xform/ian(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<r>/"
  - "xform/[iu]ang(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')/<y>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/uan(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]a(?=^|$|')/<d>/"
  - "xform/en(?=^|$|')/<f>/"
  - "xform/eng(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<h>/"
  - "xform/an(?=^|$|')/<j>/"
  - "xform/iao(?=^|$|')/<z>/"
  - "xform/ao(?=^|$|')/<k>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<c>/"
  - "xform/ai(?=^|$|')/<l>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ou(?=^|$|')/<b>/"
  - "xform/un(?=^|$|')/<n>/"
  - "xform/[uv]e(?=^|$|')|ui(?=^|$|')/<m>/"
  - "xform/'|<|>//"
"紫光双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/en(?=^|$|')/<w>/"
  - "xform/eng(?=^|$|')/<t>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^zh/<u>/"
  - "xform/^sh/<i>/"
  - "xform/'zh/'<u>/"
  - "xform/'sh/'<i>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ai(?=^|$|')/<p>/"
  - "xform/^ch/<a>/"
  - "xform/'ch/'<a>/"
  - "xform/[iu]ang(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<s>/"
  - "xform/ie(?=^|$|')/<d>/"
  - "xform/ian(?=^|$|')/<f>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<h>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<j>/"
  - "xform/ei(?=^|$|')/<k>/"
  - "xform/uan(?=^|$|')/<l>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/ou(?=^|$|')/<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<b>/"
  - "xform/ue(?=^|$|')|ui(?=^|$|')|ve(?=^|$|')/<n>/"
  - "xform/un(?=^|$|')/<m>/"
  - "xform/ao(?=^|$|')/<q>/"
  - "xform/an(?=^|$|')/<r>/"
  - "xform/'|<|>//"
"自然码":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/'|<|>//"